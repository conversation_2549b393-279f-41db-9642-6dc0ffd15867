import clsx from 'clsx'
import { useEffect, useState } from 'react'
import { recordABTest } from '~/src/hooks/Global/GoogleTag'
import { getABVariant } from '~/src/hooks/Marketing/useABTest'

/**
 * Interface for BuySellButton properties
 *
 * {number | string} width - Optional width for the banner
 * {string} containerClassName - Optional additional CSS classes for the container
 * {string} buttonClassName - Optional additional CSS classes for the button
 */
interface BuySellButtonProps {
  width?: number | string
  containerClassName?: string
  buttonClassName?: string
}

/**
 * Interface for Banner object
 *
 * {string} text - The display text for the banner
 * {string} class - The CSS class for styling
 * {string} link - The URL link associated with the banner
 * {string} event - The event name for tracking
 */
type Banner = {
  text: string
  class: string
  link: string
  event: string
}

/**
 * BuySellButton Component
 * Rotates through a series of banners with text and links,
 * applying a pulse effect before each banner change.
 *
 * @param {BuySellButtonProps} props - Properties for the component
 */
const BuySellButton = ({
  width = 230,
  containerClassName,
  buttonClassName,
}: BuySellButtonProps) => {
  // Array of experiment variants (3 in this case)
  const experimentVariants = ['A', 'B', 'C']

  // Variant ID
  const [variant, setVariant] = useState<string>()

  // State to keep track of the current banner
  const [currentBanner, setCurrentBanner] = useState<Banner>()

  // State to manage the pulse animation class
  const [isAnimating, setIsAnimating] = useState(false)

  // Time in milliseconds to switch between buttons variants
  const bannerSwitchTime = 120000

  // Remove the animation class after the specified duration
  const animationDuration = 2100

  /**
   * Array of banner objects, each containing:
   * - text: the display text for the banner
   * - class: the CSS class for styling the banner
   * - link: the URL link associated with the banner
   */
  const banners: Banner[] = [
    {
      text: 'Start Trading',
      class:
        'bg-gradient-to-b from-[#4CAF50] to-[#45a049] border border-[#3d8b3d] text-white hover:from-[#45a049] hover:to-[#3d8b3d]',
      link: 'https://online.kitco.com/gold-silver-pool?utm_source=kitco&utm_medium=referral&utm_campaign=Fixed_Button_Start_Trading',
      event: 'Button_Start_trading',
    },
    {
      text: 'Buy/Sell Gold & Silver',
      class:
        'bg-gradient-to-b from-[#F1B210] to-[#ffcc00] border border-[#e6b800] text-[#333] hover:from-[#F1B210] hover:to-[#F1B210]',
      link: 'https://online.kitco.com/?utm_source=kitco&utm_medium=referral&utm_campaign=Fixed_Button_Start_Buy_Sell',
      event: 'Button_Start_Buy_Sell',
    },
    {
      text: 'Trade Gold Today',
      class:
        'bg-gradient-to-b from-[#F1B210] to-[#ffcc00] border border-[#e6b800] text-[#333] hover:from-[#F1B210] hover:to-[#F1B210]',
      link: 'https://online.kitco.com/gold?utm_source=kitco&utm_medium=referral&utm_campaign=Fixed_Button_Trade_Gold',
      event: 'Button_Trade_Gold_Silver',
    },
  ]

  /**
   * Function to handle banner click and open link in a new tab
   */
  const onClickBanner = (link: string) => {
    // Record the AB test event in Google Tag Manager
    recordABTest('ab_click', 'BuySellButton', variant, currentBanner.event)

    window.open(link, '_blank')
  }

  /**
   * Function to get a new variant for the experiment
   */
  const getNewVariant = () => {
    const variantValue = getABVariant(experimentVariants, variant)
    setVariant(variantValue)
  }

  /**
   * UseEffect hook to get the first variant when the component mounts
   */
  useEffect(() => {
    getNewVariant()
  }, [])

  /**
   * UseEffect hook to update the current banner when the variant changes
   */
  useEffect(() => {
    if (!variant) return

    setCurrentBanner(banners[experimentVariants.indexOf(variant)])
    setIsAnimating(true) // Start animation when the variant changes

    // Stop animation after the specified duration
    const timeout = setTimeout(() => {
      setIsAnimating(false)
    }, animationDuration)

    // Cleanup timeout on component unmount or when variant changes
    return () => clearTimeout(timeout)
  }, [variant])

  /**
   * UseEffect hook to manage the banner rotation
   */
  useEffect(() => {
    // Set intervals for updating the banner
    const interval = setInterval(() => {
      getNewVariant() // Update to a new variant
    }, bannerSwitchTime)

    // Cleanup intervals on component unmount
    return () => {
      clearInterval(interval)
    }
  }, [])

  // Don't render if there is no current banner
  if (!currentBanner) return null

  return (
    <div
      className={clsx(containerClassName ?? 'flex justify-center items-center')}
    >
      <button
        type="button"
        onClick={() => onClickBanner(currentBanner.link)}
        onKeyDown={(e) =>
          e.key === 'Enter' && onClickBanner(currentBanner.link)
        }
        className={clsx(
          'flex items-center justify-center font-bold uppercase cursor-pointer',
          'transition-transform duration-500 ease-in-out text-center h-[30px]',
          'rounded-lg p-1.5 box-border opacity-100',
          isAnimating ? 'animate-scale-pulse' : '',
          'hover:scale-105 hover:shadow-[0_3px_6px_rgba(0,0,0,0.2)] hover:opacity-100',
          currentBanner.class,
          buttonClassName ?? '',
        )}
        role="button"
        tabIndex={0}
        style={{ width: `${width}px` }}
      >
        {currentBanner.text}
      </button>
    </div>
  )
}

export default BuySellButton
