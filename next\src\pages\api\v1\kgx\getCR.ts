import type { NextRequest } from 'next/server'

export const config = {
  runtime: 'edge',
}

const BaseURL = 'https://proxy.kitco.com'

export default async function handler(req: NextRequest) {
  const url = new URL(req.url)
  const apikey = url.searchParams.get('apikey')
  const symbol = url.searchParams.get('symbol')
  const type = url.searchParams.get('type') || 'json'

  if (!apikey || !symbol) {
    return new Response(
      JSON.stringify({ error: 'Missing required parameters: apikey and symbol' }),
      {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      }
    )
  }

  // Build the proxy URL for crypto data
  const proxyUrl = `${BaseURL}/getValue?ver=2.0&tz=Africa/Algiers&df=2&tf=2&type=${type}&kgx=yes&symbol=${symbol}&apikey=${apikey}`

  try {
    const response = await fetch(proxyUrl)
    if (!response.ok) {
      throw new Error(`Failed to fetch data from ${proxyUrl}`)
    }
    
    const data = await response.json()

    // Transform the response to match the expected format for crypto data
    const transformedData = {
      Cryptocurrencies: {
        CR: Array.isArray(data) ? data : [data]
      }
    }

    return new Response(JSON.stringify(transformedData), {
      headers: { 'Content-Type': 'application/json' },
    })
  } catch (error) {
    console.error('Fetch error:', error)
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    })
  }
}
