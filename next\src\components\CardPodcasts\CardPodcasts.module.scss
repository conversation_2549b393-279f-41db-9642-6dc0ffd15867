.cardItem {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 20px;
  gap: 20px;
  background: #f8f8f8;
  border: 2px solid #f3f3f2;
  border-radius: 0 0 16px 16px;
  border-width: 0px 2px 2px 2px;
  height: max-content;

  & .cardContentWrap {
    & .cardTitle {
      font-family: 'Lato';
      font-style: normal;
      font-weight: 700;
      font-size: 20px;
      line-height: 130%;
      letter-spacing: 0.0075em;
      color: #232323;
      margin-bottom: 8px;
    }

    & .cardDescription {
      font-family: 'Mulish';
      font-style: normal;
      font-weight: 300;
      font-size: 16px;
      line-height: 135%;
      color: #838383;
    }
  }

  & .cardConnect {
    font-family: 'Lato';
    font-style: normal;
    font-weight: 700;
    font-size: 14px;
    line-height: 130%;
    letter-spacing: 0.005em;
    color: #232323;

    & a {
      color: #0c87d2;

      &:not(:last-child):after {
        content: ' | ';
        color: black;
        cursor: default;
      }

      &:hover {
        color: #57c0ff;
      }
    }
  }
}
