import kitcoQuery from '~/src/services/database/kitcoQuery'
import type CommodityData from '~/src/types/DataTable/CommodityData'
import { cryptos } from '~/src/lib/cryptos-factory.lib'
import * as timestamps from '~/src/utils/timestamps'

interface CryptoCurrency {
  name: string
  symbol: string
}

// Use the same cryptos that are supported by the GraphQL API
const CRYPTO_CURRENCIES: CryptoCurrency[] = [
  { name: 'Bitcoin', symbol: 'BTC' },
  { name: 'Ethereum', symbol: 'ETH' },
  { name: '<PERSON><PERSON>oin', symbol: 'LTC' },
  { name: '<PERSON><PERSON>', symbol: 'XMR' },
  { name: '<PERSON><PERSON><PERSON>', symbol: 'XRP' },
]

/**
 * Get the crypto data using GraphQL
 */
const useGICryptoData = (): CommodityData[] => {
  // Use the GraphQL query for crypto data
  const { data } = kitcoQuery(
    cryptos.cryptosBtcEthLtcXmrXrp({
      variables: {
        currency: 'USD',
        timestamp: timestamps.current(),
      },
    })
  )

  return processCryptoData(data)
}

/**
 * Process the crypto data from GraphQL response
 */
function processCryptoData(data: any): CommodityData[] {
  if (!data) {
    console.warn('No crypto data available, using defaults')
    return CRYPTO_CURRENCIES.map(({ name }) => createDefaultCommodityData(name))
  }

  const result: CommodityData[] = []

  // Process each crypto currency
  CRYPTO_CURRENCIES.forEach(({ name, symbol }) => {
    const cryptoData = data[symbol]
    
    if (cryptoData?.results?.[0]) {
      const quote = cryptoData.results[0]
      result.push({
        commodity: name,
        lastBid: {
          bid: quote.close?.toString() || '0',
          bidVal: quote.close || 0,
          currency: 'USD',
          originalTime: new Date().toISOString(),
        },
        changeDueToUSD: {
          change: '0', // KGX doesn't separate USD vs trade changes for crypto
          changeVal: 0,
          percentage: '0',
          percentageVal: 0,
        },
        changeDueToTrade: {
          change: quote.change?.toString() || '0',
          changeVal: quote.change || 0,
          percentage: quote.changePercentage?.toString() || '0',
          percentageVal: quote.changePercentage || 0,
        },
        totalChange: {
          change: quote.change?.toString() || '0',
          changeVal: quote.change || 0,
          percentage: quote.changePercentage?.toString() || '0',
          percentageVal: quote.changePercentage || 0,
        },
      })
    } else {
      result.push(createDefaultCommodityData(name))
    }
  })

  return result
}

const createDefaultCommodityData = (name: string): CommodityData => ({
  commodity: name,
  lastBid: {
    bid: '0',
    bidVal: 0,
    currency: 'USD',
    originalTime: new Date().toISOString(),
  },
  changeDueToUSD: {
    change: '0',
    changeVal: 0,
    percentage: '0',
    percentageVal: 0,
  },
  changeDueToTrade: {
    change: '0',
    changeVal: 0,
    percentage: '0',
    percentageVal: 0,
  },
  totalChange: {
    change: '0',
    changeVal: 0,
    percentage: '0',
    percentageVal: 0,
  },
})

export { useGICryptoData }
export default useGICryptoData
