.link {
  width: 100%;
  height: 60px;
  margin: 0 auto 1em auto;
  border: solid 1px #0c86d2;
  background-color: #e5e5e5;
  gap: 1rem;
  display: flex;

  @media (min-width: 640px) {
    height: 80px;
  }

  @media (min-width: 767px) {
    gap: 2rem;
    height: 105px;
  }
}

.imageContainer {
  position: relative;
  display: block;
  width: 33%;
  height: auto;
  min-height: 100%;
  margin-right: 20px;
}

.image {
  display: block;
  max-width: 30%;
  min-width: 30%;
  height: 100%;
}

.st0 {
  fill: #2486d2;
}

.st1 {
  fill-rule: evenodd;
  clip-rule: evenodd;
  fill: #fff;
}

.st2 {
  fill: #fff;
}
